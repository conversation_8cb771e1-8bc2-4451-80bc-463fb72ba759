import Cocoa
import WebKit
import os.log

private let lxAppWindowControllerLog = OSLog(subsystem: "LingXia", category: "LxAppWindow")

@MainActor
public class macOSLxAppWindowController: NSWindowController {
    private static let log = lxAppWindowControllerLog
    
    internal var appId: String
    private var initialPath: String
    private var lxAppViewController: macOSLxAppViewController!
    private var titleBarView: NSView!
    private let titleBarHeight: CGFloat = 32
    
    override init(window: NSWindow?) {
        self.appId = ""
        self.initialPath = ""
        super.init(window: window)
    }
    
    init(appId: String, path: String) {
        self.appId = appId
        self.initialPath = path
        
        let screenSize = NSScreen.main?.visibleFrame ?? .zero
        let windowWidth: CGFloat = 375
        let windowHeight: CGFloat = 667
        
        let windowX = max(0, (screenSize.width - windowWidth) / 2)
        let windowY = max(0, (screenSize.height - windowHeight) / 2)
        let contentRect = NSRect(x: windowX, y: windowY, width: windowWidth, height: windowHeight)
        
        let window = macOSLxAppWindow(
            contentRect: contentRect,
            styleMask: [.titled, .closable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        super.init(window: window)

        setupWindow()
        setupWindowAppearance()
        setupViewController()

        // This is the key - make window visible immediately in init
        window.setFrame(contentRect, display: true)
        window.makeKeyAndOrderFront(nil)

        // Activate the application
        NSApp.activate(ignoringOtherApps: true)
        
        os_log("Window created and made visible for appId=%@", log: Self.log, type: .info, appId)
    }
    
    convenience init(window: NSWindow, viewController: macOSLxAppViewController, appId: String) {
        self.init(appId: appId, path: "/")
        self.lxAppViewController = viewController
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupWindow() {
        guard let window = window else { return }
        
        window.title = "LingXia - \(appId)"
        window.isReleasedWhenClosed = false
        window.delegate = self
    }
    
    private func setupWindowAppearance() {
        guard let window = window as? macOSLxAppWindow else { return }

        setupCustomTitleBar()
        window.backgroundColor = NSColor.windowBackgroundColor
        window.isOpaque = true
    }

    private func setupCustomTitleBar() {
        print("🔧 setupCustomTitleBar started")
        guard let window = window as? macOSLxAppWindow else {
            print("❌ Failed to cast window to macOSLxAppWindow")
            return
        }

        // Hide system buttons first
        for type in [NSWindow.ButtonType.closeButton, .miniaturizeButton, .zoomButton] {
            if let button = window.standardWindowButton(type) {
                button.isHidden = true
                button.removeFromSuperview()
            }
        }

        window.isMovableByWindowBackground = true

        let windowWidth: CGFloat = 375
        let capsuleButtonWidth: CGFloat = 87
        let capsuleButtonHeight: CGFloat = 28
        let capsuleTopMargin: CGFloat = 2

        titleBarView = NSView(frame: NSRect(x: 0, y: 0, width: windowWidth, height: titleBarHeight))
        titleBarView.wantsLayer = true
        titleBarView.layer?.backgroundColor = NSColor.white.cgColor
        titleBarView.layer?.zPosition = 999

        // Add shadow line at bottom
        let shadowView = NSView(frame: NSRect(x: 0, y: 0, width: windowWidth, height: 1))
        shadowView.wantsLayer = true
        shadowView.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.1).cgColor
        titleBarView.addSubview(shadowView)

        // Add title label
        let titleLabel = NSTextField(labelWithString: getWindowTitle())
        titleLabel.font = NSFont.systemFont(ofSize: 17, weight: .semibold)
        titleLabel.textColor = NSColor.black
        titleLabel.alignment = .center
        titleLabel.sizeToFit()
        titleLabel.frame = NSRect(
            x: (windowWidth - titleLabel.frame.width) / 2,
            y: (titleBarHeight - titleLabel.frame.height) / 2,
            width: titleLabel.frame.width,
            height: titleLabel.frame.height
        )
        titleBarView.addSubview(titleLabel)

        // Create capsule buttons
        let homeButton = createStandardButton(
            image: createThreeDotsImage(),
            target: self,
            action: #selector(moreButtonTapped)
        )
        homeButton.toolTip = "More"

        let minimizeButton = createStandardButton(
            image: createMinimizeButtonImage(),
            target: self,
            action: #selector(minimizeWindow)
        )
        minimizeButton.toolTip = "Minimize"

        let closeButton = createStandardButton(
            image: createCloseButtonImage(),
            target: self,
            action: #selector(closeWindow)
        )
        closeButton.toolTip = "Close"

        // Position capsule buttons
        let buttonWidth = capsuleButtonWidth / 3
        let buttonHeight = capsuleButtonHeight
        let buttonY = capsuleTopMargin
        let interButtonSpacing: CGFloat = 1

        let homeButtonX = windowWidth - capsuleButtonWidth - 7
        homeButton.frame = NSRect(x: homeButtonX, y: buttonY, width: buttonWidth, height: buttonHeight)

        let minimizeButtonX = homeButtonX + buttonWidth
        minimizeButton.frame = NSRect(x: minimizeButtonX, y: buttonY, width: buttonWidth, height: buttonHeight)

        let closeButtonX = minimizeButtonX + buttonWidth
        closeButton.frame = NSRect(x: closeButtonX, y: buttonY, width: buttonWidth, height: buttonHeight)

        // Add separators between buttons
        let leftSeparator = NSView(frame: NSRect(x: homeButton.frame.maxX - (interButtonSpacing / 2), y: buttonY + 4, width: interButtonSpacing, height: buttonHeight - 8))
        leftSeparator.wantsLayer = true
        leftSeparator.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.4).cgColor

        let rightSeparator = NSView(frame: NSRect(x: minimizeButton.frame.maxX - (interButtonSpacing / 2), y: buttonY + 4, width: interButtonSpacing, height: buttonHeight - 8))
        rightSeparator.wantsLayer = true
        rightSeparator.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.4).cgColor

        // Add all elements to title bar
        titleBarView.addSubview(leftSeparator)
        titleBarView.addSubview(rightSeparator)
        titleBarView.addSubview(homeButton)
        titleBarView.addSubview(minimizeButton)
        titleBarView.addSubview(closeButton)

        // Configure touch events and z-position
        leftSeparator.acceptsTouchEvents = false
        rightSeparator.acceptsTouchEvents = false

        homeButton.layer?.zPosition = 1000
        minimizeButton.layer?.zPosition = 1000
        closeButton.layer?.zPosition = 1000

        titleBarView.layoutSubtreeIfNeeded()
        shadowView.frame = NSRect(x: 0, y: 0, width: windowWidth, height: 1)

        // Set the custom title bar
        print("🔧 Setting custom title bar view")
        window.setTitleBarView(titleBarView)
        print("✅ setupCustomTitleBar completed")
    }

    private func getWindowTitle() -> String {
        // TODO: Get actual app title from LxApp configuration
        return "LingXia"
    }

    private func createStandardButton(image: NSImage?, target: AnyObject?, action: Selector?) -> NSButton {
        let button = NSButton()
        button.image = image
        button.bezelStyle = .regularSquare
        button.isBordered = false
        button.translatesAutoresizingMaskIntoConstraints = true

        if let target = target, let action = action {
            button.target = target
            button.action = action
        }

        button.imageScaling = .scaleProportionallyDown
        button.imagePosition = .imageOnly
        button.isEnabled = true
        button.wantsLayer = true
        button.layer?.backgroundColor = NSColor.clear.cgColor
        button.setButtonType(.momentaryPushIn)
        button.acceptsTouchEvents = true

        return button
    }

    private func createThreeDotsImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setFillColor(NSColor.black.cgColor)

            let dotRadius: CGFloat = 1.5
            let dotSpacing: CGFloat = 4
            let totalWidth = 3 * (dotRadius * 2) + 2 * dotSpacing
            let startX = (size.width - totalWidth) / 2
            let centerY = size.height / 2

            for i in 0..<3 {
                let x = startX + CGFloat(i) * (dotRadius * 2 + dotSpacing) + dotRadius
                let dotRect = CGRect(x: x - dotRadius, y: centerY - dotRadius, width: dotRadius * 2, height: dotRadius * 2)
                context.fillEllipse(in: dotRect)
            }
        }

        image.unlockFocus()
        return image
    }

    private func createMinimizeButtonImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setStrokeColor(NSColor.black.cgColor)
            context.setLineWidth(1.5)

            let lineY = size.height / 2
            let lineStartX: CGFloat = 8
            let lineEndX: CGFloat = 16

            context.move(to: CGPoint(x: lineStartX, y: lineY))
            context.addLine(to: CGPoint(x: lineEndX, y: lineY))
            context.strokePath()
        }

        image.unlockFocus()
        return image
    }

    private func createCloseButtonImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setStrokeColor(NSColor.black.cgColor)
            context.setLineWidth(1.5)

            let margin: CGFloat = 8
            let startPoint1 = CGPoint(x: margin, y: margin)
            let endPoint1 = CGPoint(x: size.width - margin, y: size.height - margin)
            let startPoint2 = CGPoint(x: size.width - margin, y: margin)
            let endPoint2 = CGPoint(x: margin, y: size.height - margin)

            context.move(to: startPoint1)
            context.addLine(to: endPoint1)
            context.move(to: startPoint2)
            context.addLine(to: endPoint2)
            context.strokePath()
        }

        image.unlockFocus()
        return image
    }

    // MARK: - Button Actions
    @objc private func moreButtonTapped() {
        // TODO: Implement more button functionality
        os_log("More button tapped", log: Self.log, type: .info)
    }

    @objc private func minimizeWindow() {
        window?.miniaturize(nil)
    }

    @objc private func closeWindow() {
        window?.close()
    }

    private func setupViewController() {
        guard let window = window else {
            os_log("❌ No window available for setupViewController", log: Self.log, type: .error)
            return
        }

        print("🔧 Creating macOSLxAppViewController for appId: \(appId), path: \(initialPath)")
        os_log("🔧 Creating macOSLxAppViewController for appId: %@, path: %@", log: Self.log, type: .info, appId, initialPath)

        // Create and set the app view controller directly
        // The title bar is already handled by setTitleBarView() in setupCustomTitleBar()
        lxAppViewController = macOSLxAppViewController(appId: appId, path: initialPath)
        window.contentViewController = lxAppViewController

        print("🔧 Set contentViewController, forcing view to load")
        os_log("🔧 Set contentViewController, forcing view to load", log: Self.log, type: .info)

        // Force view to load
        _ = lxAppViewController.view

        print("✅ setupViewController completed")
        os_log("✅ setupViewController completed", log: Self.log, type: .info)
    }
}

extension macOSLxAppWindowController: NSWindowDelegate {
    public func windowWillClose(_ notification: Notification) {
        os_log("Window will close for appId=%@", log: Self.log, type: .info, appId)
        // Remove from active controllers list, but don't call close() again to avoid infinite recursion
        macOSLxApp.removeWindowController(self)
    }
    
    public func windowDidBecomeKey(_ notification: Notification) {
        os_log("Window became key for appId=%@", log: Self.log, type: .info, appId)
    }
}
